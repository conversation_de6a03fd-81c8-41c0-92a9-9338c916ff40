'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface ToolDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: (name: string, value: any) => void;
  watch: (name: string) => any;
}

export default function ToolDetailsForm({ register, errors, setValue, watch }: ToolDetailsFormProps) {
  const hasApi = watch('details.hasApi');
  const hasFreeTier = watch('details.hasFreeTier');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">AI Tool Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Technical Level */}
        <div>
          <Label htmlFor="technicalLevel">Technical Level</Label>
          <Select onValueChange={(value) => setValue('details.technicalLevel', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select technical level..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="BEGINNER">Beginner</SelectItem>
              <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
              <SelectItem value="ADVANCED">Advanced</SelectItem>
              <SelectItem value="EXPERT">Expert</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Learning Curve */}
        <div>
          <Label htmlFor="learningCurve">Learning Curve</Label>
          <Select onValueChange={(value) => setValue('details.learningCurve', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select learning curve..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="LOW">Low</SelectItem>
              <SelectItem value="MEDIUM">Medium</SelectItem>
              <SelectItem value="HIGH">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Pricing Model */}
        <div>
          <Label htmlFor="pricingModel">Pricing Model</Label>
          <Select onValueChange={(value) => setValue('details.pricingModel', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select pricing model..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="FREEMIUM">Freemium</SelectItem>
              <SelectItem value="PAID">Paid</SelectItem>
              <SelectItem value="SUBSCRIPTION">Subscription</SelectItem>
              <SelectItem value="ONE_TIME">One-time Purchase</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Price Range */}
        <div>
          <Label htmlFor="priceRange">Price Range</Label>
          <Select onValueChange={(value) => setValue('details.priceRange', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select price range..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="FREE">Free</SelectItem>
              <SelectItem value="LOW">Low ($1-$50/month)</SelectItem>
              <SelectItem value="MEDIUM">Medium ($51-$200/month)</SelectItem>
              <SelectItem value="HIGH">High ($201-$1000/month)</SelectItem>
              <SelectItem value="ENTERPRISE">Enterprise ($1000+/month)</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Boolean Options */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="hasApi"
            checked={hasApi}
            onCheckedChange={(checked) => setValue('details.apiAccess', checked)}
          />
          <Label htmlFor="hasApi">Has API Access</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="hasFreeTier"
            checked={hasFreeTier}
            onCheckedChange={(checked) => setValue('details.hasFreeTier', checked)}
          />
          <Label htmlFor="hasFreeTier">Has Free Tier</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="openSource"
            onCheckedChange={(checked) => setValue('details.openSource', checked)}
          />
          <Label htmlFor="openSource">Open Source</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="mobileSupport"
            onCheckedChange={(checked) => setValue('details.mobileSupport', checked)}
          />
          <Label htmlFor="mobileSupport">Mobile Support</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="trialAvailable"
            onCheckedChange={(checked) => setValue('details.trialAvailable', checked)}
          />
          <Label htmlFor="trialAvailable">Trial Available</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox
            id="demoAvailable"
            onCheckedChange={(checked) => setValue('details.demoAvailable', checked)}
          />
          <Label htmlFor="demoAvailable">Demo Available</Label>
        </div>
      </div>

      {/* Text Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="pricingUrl">Pricing URL</Label>
          <Input
            id="pricingUrl"
            type="url"
            {...register('details.pricingUrl')}
            placeholder="https://example.com/pricing"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="supportEmail">Support Email</Label>
          <Input
            id="supportEmail"
            type="email"
            {...register('details.supportEmail')}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="communityUrl">Community URL</Label>
          <Input
            id="communityUrl"
            type="url"
            {...register('details.communityUrl')}
            placeholder="https://community.example.com"
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="customizationLevel">Customization Level</Label>
          <Input
            id="customizationLevel"
            {...register('details.customizationLevel')}
            placeholder="High, Medium, Low"
            className="mt-1"
          />
        </div>
      </div>

      {/* Pricing Details */}
      <div>
        <Label htmlFor="pricingDetails">Pricing Details</Label>
        <Textarea
          id="pricingDetails"
          {...register('details.pricingDetails')}
          placeholder="Detailed pricing information, plans, features included..."
          className="mt-1 min-h-[80px]"
        />
      </div>

      {/* Array Fields - These would need special handling */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="keyFeatures">Key Features (comma-separated)</Label>
          <Textarea
            id="keyFeatures"
            {...register('details.keyFeaturesText')}
            placeholder="AI-powered analysis, Real-time collaboration, Advanced analytics..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter features separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="useCases">Use Cases (comma-separated)</Label>
          <Textarea
            id="useCases"
            {...register('details.useCasesText')}
            placeholder="Content creation, Data analysis, Customer support..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter use cases separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="targetAudience">Target Audience (comma-separated)</Label>
          <Textarea
            id="targetAudience"
            {...register('details.targetAudienceText')}
            placeholder="Developers, Marketers, Data scientists..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter target audiences separated by commas. They will be converted to a list.
          </p>
        </div>

        <div>
          <Label htmlFor="supportChannels">Support Channels (comma-separated)</Label>
          <Textarea
            id="supportChannels"
            {...register('details.supportChannelsText')}
            placeholder="Email, Live chat, Phone, Documentation..."
            className="mt-1 min-h-[60px]"
          />
          <p className="text-xs text-gray-500 mt-1">
            Enter support channels separated by commas. They will be converted to a list.
          </p>
        </div>
      </div>
    </div>
  );
}

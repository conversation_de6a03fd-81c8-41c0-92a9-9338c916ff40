'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { EntityType, Category, Tag, Feature, CreateEntityDto } from '@/types/entity';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import MultiSelectCheckbox from './MultiSelectCheckbox';
import ToolDetailsForm from './ToolDetailsForm';
import CourseDetailsForm from './CourseDetailsForm';
import AgencyDetailsForm from './AgencyDetailsForm';
import NewsletterDetailsForm from './NewsletterDetailsForm';
import DefaultDetailsForm from './DefaultDetailsForm';

// Zod schema for form validation
const submitFormSchema = z.object({
  // Core entity information
  name: z.string().min(1, 'Name is required').max(200, 'Name must be less than 200 characters'),
  shortDescription: z.string().optional(),
  description: z.string().min(10, 'Description must be at least 10 characters').max(5000, 'Description must be less than 5000 characters'),
  websiteUrl: z.string().url('Please enter a valid URL'),
  logoUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  documentationUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  contactUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  privacyPolicyUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  foundedYear: z.number().min(1900).max(new Date().getFullYear()).optional(),
  
  // Entity type and categorization
  entityTypeId: z.string().min(1, 'Please select an entity type'),
  categoryIds: z.array(z.string()).optional(),
  tagIds: z.array(z.string()).optional(),
  featureIds: z.array(z.string()).optional(),
  
  // Company/organization details
  employeeCountRange: z.string().optional(),
  fundingStage: z.string().optional(),
  locationSummary: z.string().optional(),
  
  // Pricing and access
  hasFreeTier: z.boolean().optional(),
  refLink: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  
  // SEO metadata
  metaTitle: z.string().max(60, 'Meta title must be less than 60 characters').optional(),
  metaDescription: z.string().max(160, 'Meta description must be less than 160 characters').optional(),
});

type SubmitFormData = z.infer<typeof submitFormSchema>;

interface SubmitFormProps {
  entityTypes: EntityType[];
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  onSubmit: (data: CreateEntityDto) => Promise<void>;
  isSubmitting: boolean;
}

export default function SubmitForm({
  entityTypes,
  categories,
  tags,
  features,
  onSubmit,
  isSubmitting
}: SubmitFormProps) {
  const [selectedEntityTypeId, setSelectedEntityTypeId] = useState<string>('');
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [selectedFeatureIds, setSelectedFeatureIds] = useState<string[]>([]);

  const form = useForm<SubmitFormData>({
    resolver: zodResolver(submitFormSchema),
    defaultValues: {
      name: '',
      shortDescription: '',
      description: '',
      websiteUrl: '',
      logoUrl: '',
      documentationUrl: '',
      contactUrl: '',
      privacyPolicyUrl: '',
      entityTypeId: '',
      categoryIds: [],
      tagIds: [],
      featureIds: [],
      hasFreeTier: false,
      refLink: '',
      metaTitle: '',
      metaDescription: '',
    }
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch } = form;
  const watchedEntityTypeId = watch('entityTypeId');

  const handleEntityTypeChange = (value: string) => {
    setSelectedEntityTypeId(value);
    setValue('entityTypeId', value);
  };

  // Helper function to convert comma-separated strings to arrays
  const stringToArray = (str: string | undefined): string[] | undefined => {
    if (!str || str.trim() === '') return undefined;
    return str.split(',').map(item => item.trim()).filter(item => item.length > 0);
  };

  const handleFormSubmit: SubmitHandler<SubmitFormData> = async (data) => {
    try {
      // Process details object with array conversions
      const processedDetails = data.details ? {
        ...data.details,
        // Convert text fields to arrays for tool details
        keyFeatures: stringToArray(data.details.keyFeaturesText),
        useCases: stringToArray(data.details.useCasesText),
        targetAudience: stringToArray(data.details.targetAudienceText),
        supportChannels: stringToArray(data.details.supportChannelsText),
        // Convert text fields to arrays for course details
        prerequisites: stringToArray(data.details.prerequisitesText),
        learningOutcomes: stringToArray(data.details.learningOutcomesText),
        // Convert text fields to arrays for agency details
        servicesOffered: stringToArray(data.details.servicesOfferedText),
        specializations: stringToArray(data.details.specializationsText),
        // Convert text fields to arrays for newsletter details
        topicsCovered: stringToArray(data.details.topicsCoveredText),
        // Remove the text versions
        keyFeaturesText: undefined,
        useCasesText: undefined,
        targetAudienceText: undefined,
        supportChannelsText: undefined,
        prerequisitesText: undefined,
        learningOutcomesText: undefined,
        servicesOfferedText: undefined,
        specializationsText: undefined,
        topicsCoveredText: undefined,
      } : undefined;

      const payload: CreateEntityDto = {
        ...data,
        categoryIds: selectedCategoryIds.length > 0 ? selectedCategoryIds : undefined,
        tagIds: selectedTagIds.length > 0 ? selectedTagIds : undefined,
        featureIds: selectedFeatureIds.length > 0 ? selectedFeatureIds : undefined,
        // Convert empty strings to undefined for optional URL fields
        logoUrl: data.logoUrl || undefined,
        documentationUrl: data.documentationUrl || undefined,
        contactUrl: data.contactUrl || undefined,
        privacyPolicyUrl: data.privacyPolicyUrl || undefined,
        refLink: data.refLink || undefined,
        metaTitle: data.metaTitle || undefined,
        metaDescription: data.metaDescription || undefined,
        details: processedDetails,
      };

      await onSubmit(payload);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const selectedEntityType = entityTypes.find(type => type.id === selectedEntityTypeId);

  const renderTypeSpecificDetails = () => {
    if (!selectedEntityType) return null;

    const commonProps = {
      register,
      errors,
      setValue,
      watch
    };

    switch (selectedEntityType.slug) {
      case 'ai-tool':
      case 'tool':
        return <ToolDetailsForm {...commonProps} />;
      case 'course':
        return <CourseDetailsForm {...commonProps} />;
      case 'agency':
        return <AgencyDetailsForm {...commonProps} />;
      case 'newsletter':
        return <NewsletterDetailsForm {...commonProps} />;
      case 'community':
      case 'content-creator':
      default:
        return <DefaultDetailsForm {...commonProps} entityTypeName={selectedEntityType.name} />;
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
      {/* Step 1: Entity Type Selection */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="entityType" className="text-base font-semibold">
            What kind of resource are you submitting? *
          </Label>
          <Select onValueChange={handleEntityTypeChange} value={selectedEntityTypeId}>
            <SelectTrigger className="mt-2">
              <SelectValue placeholder="Select a resource type..." />
            </SelectTrigger>
            <SelectContent>
              {entityTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.entityTypeId && (
            <p className="mt-1 text-sm text-red-600">{errors.entityTypeId.message}</p>
          )}
        </div>

        {selectedEntityType && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900">{selectedEntityType.name}</h3>
            <p className="text-sm text-blue-700 mt-1">{selectedEntityType.description}</p>
          </div>
        )}
      </div>

      {/* Step 2: Core Information (shown after entity type is selected) */}
      {selectedEntityTypeId && (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Core Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label htmlFor="name">Resource Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter the name of the resource"
                  className="mt-1"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="websiteUrl">Website URL *</Label>
                <Input
                  id="websiteUrl"
                  type="url"
                  {...register('websiteUrl')}
                  placeholder="https://example.com"
                  className="mt-1"
                />
                {errors.websiteUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.websiteUrl.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="shortDescription">Short Description</Label>
                <Input
                  id="shortDescription"
                  {...register('shortDescription')}
                  placeholder="Brief one-line description (optional)"
                  className="mt-1"
                />
                {errors.shortDescription && (
                  <p className="mt-1 text-sm text-red-600">{errors.shortDescription.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="description">Full Description *</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Provide a detailed description of the resource, its features, and benefits..."
                  className="mt-1 min-h-[120px]"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="logoUrl">Logo URL</Label>
                <Input
                  id="logoUrl"
                  type="url"
                  {...register('logoUrl')}
                  placeholder="https://example.com/logo.png (optional)"
                  className="mt-1"
                />
                {errors.logoUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.logoUrl.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="documentationUrl">Documentation URL</Label>
                <Input
                  id="documentationUrl"
                  type="url"
                  {...register('documentationUrl')}
                  placeholder="https://docs.example.com (optional)"
                  className="mt-1"
                />
                {errors.documentationUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.documentationUrl.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Categorization Section */}
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Categorization</h2>

            <div className="space-y-6">
              <MultiSelectCheckbox
                label="Categories"
                options={categories.map(cat => ({
                  id: cat.id,
                  name: cat.name,
                  description: cat.description
                }))}
                selectedIds={selectedCategoryIds}
                onSelectionChange={setSelectedCategoryIds}
                showDescription={true}
                columns={2}
              />

              <MultiSelectCheckbox
                label="Tags"
                options={tags.map(tag => ({
                  id: tag.id,
                  name: tag.name
                }))}
                selectedIds={selectedTagIds}
                onSelectionChange={setSelectedTagIds}
                columns={3}
              />

              <MultiSelectCheckbox
                label="Features"
                options={features.map(feature => ({
                  id: feature.id,
                  name: feature.name,
                  description: feature.description
                }))}
                selectedIds={selectedFeatureIds}
                onSelectionChange={setSelectedFeatureIds}
                showDescription={true}
                columns={2}
              />
            </div>
          </div>

          {/* Additional Fields Section */}
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="contactUrl">Contact URL</Label>
                <Input
                  id="contactUrl"
                  type="url"
                  {...register('contactUrl')}
                  placeholder="https://example.com/contact (optional)"
                  className="mt-1"
                />
                {errors.contactUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.contactUrl.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="privacyPolicyUrl">Privacy Policy URL</Label>
                <Input
                  id="privacyPolicyUrl"
                  type="url"
                  {...register('privacyPolicyUrl')}
                  placeholder="https://example.com/privacy (optional)"
                  className="mt-1"
                />
                {errors.privacyPolicyUrl && (
                  <p className="mt-1 text-sm text-red-600">{errors.privacyPolicyUrl.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="foundedYear">Founded Year</Label>
                <Input
                  id="foundedYear"
                  type="number"
                  {...register('foundedYear', { valueAsNumber: true })}
                  placeholder="2023 (optional)"
                  className="mt-1"
                  min="1900"
                  max={new Date().getFullYear()}
                />
                {errors.foundedYear && (
                  <p className="mt-1 text-sm text-red-600">{errors.foundedYear.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="refLink">Referral Link</Label>
                <Input
                  id="refLink"
                  type="url"
                  {...register('refLink')}
                  placeholder="https://example.com/ref/123 (optional)"
                  className="mt-1"
                />
                {errors.refLink && (
                  <p className="mt-1 text-sm text-red-600">{errors.refLink.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Type-Specific Details Section */}
          {selectedEntityType && (
            <div className="border-t pt-6">
              {renderTypeSpecificDetails()}
            </div>
          )}

          {/* Submit button */}
          <div className="border-t pt-6">
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full md:w-auto"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Resource'}
            </Button>
          </div>
        </div>
      )}
    </form>
  );
}

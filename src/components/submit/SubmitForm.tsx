'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { EntityType, Category, Tag, Feature, CreateEntityDto } from '@/types/entity';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import MultiSelectCheckbox from './MultiSelectCheckbox';
import ToolDetailsForm from './ToolDetailsForm';
import CourseDetailsForm from './CourseDetailsForm';
import AgencyDetailsForm from './AgencyDetailsForm';
import NewsletterDetailsForm from './NewsletterDetailsForm';
import DefaultDetailsForm from './DefaultDetailsForm';

// Zod schema for form validation
const submitFormSchema = z.object({
  // Core entity information
  name: z.string().min(1, 'Name is required').max(200, 'Name must be less than 200 characters'),
  short_description: z.string().optional(),
  description: z.string().min(10, 'Description must be at least 10 characters').max(5000, 'Description must be less than 5000 characters'),
  website_url: z.string().url('Please enter a valid URL'),
  logo_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  documentation_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  contact_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  privacy_policy_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  founded_year: z.number().min(1900).max(new Date().getFullYear()).optional(),

  // Entity type and categorization
  entity_type_id: z.string().min(1, 'Please select an entity type'),
  category_ids: z.array(z.string()).optional(),
  tag_ids: z.array(z.string()).optional(),
  feature_ids: z.array(z.string()).optional(),

  // Company/organization details
  employee_count_range: z.string().optional(),
  funding_stage: z.string().optional(),
  location_summary: z.string().optional(),

  // Pricing and access
  has_free_tier: z.boolean().optional(),
  ref_link: z.string().url('Please enter a valid URL').optional().or(z.literal('')),

  // SEO metadata
  meta_title: z.string().max(60, 'Meta title must be less than 60 characters').optional(),
  meta_description: z.string().max(160, 'Meta description must be less than 160 characters').optional(),
});

type SubmitFormData = z.infer<typeof submitFormSchema>;

interface SubmitFormProps {
  entityTypes: EntityType[];
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  onSubmit: (data: CreateEntityDto) => Promise<void>;
  isSubmitting: boolean;
}

export default function SubmitForm({
  entityTypes,
  categories,
  tags,
  features,
  onSubmit,
  isSubmitting
}: SubmitFormProps) {
  const [selectedEntityTypeId, setSelectedEntityTypeId] = useState<string>('');
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>([]);
  const [selectedTagIds, setSelectedTagIds] = useState<string[]>([]);
  const [selectedFeatureIds, setSelectedFeatureIds] = useState<string[]>([]);

  const form = useForm<SubmitFormData>({
    resolver: zodResolver(submitFormSchema),
    defaultValues: {
      name: '',
      short_description: '',
      description: '',
      website_url: '',
      logo_url: '',
      documentation_url: '',
      contact_url: '',
      privacy_policy_url: '',
      entity_type_id: '',
      category_ids: [],
      tag_ids: [],
      feature_ids: [],
      has_free_tier: false,
      ref_link: '',
      meta_title: '',
      meta_description: '',
    }
  });

  const { register, handleSubmit, formState: { errors }, setValue, watch } = form;
  const watchedEntityTypeId = watch('entity_type_id');

  const handleEntityTypeChange = (value: string) => {
    setSelectedEntityTypeId(value);
    setValue('entity_type_id', value);
  };

  // Helper function to convert comma-separated strings to arrays
  const stringToArray = (str: string | undefined): string[] | undefined => {
    if (!str || str.trim() === '') return undefined;
    return str.split(',').map(item => item.trim()).filter(item => item.length > 0);
  };

  const handleFormSubmit: SubmitHandler<SubmitFormData> = async (data) => {
    try {
      // Process details object with array conversions
      const processedDetails = data.details ? {
        ...data.details,
        // Convert text fields to arrays for tool details
        key_features: stringToArray(data.details.key_features_text),
        use_cases: stringToArray(data.details.use_cases_text),
        target_audience: stringToArray(data.details.target_audience_text),
        support_channels: stringToArray(data.details.support_channels_text),
        // Convert text fields to arrays for course details
        prerequisites: stringToArray(data.details.prerequisites_text),
        learning_outcomes: stringToArray(data.details.learning_outcomes_text),
        // Convert text fields to arrays for agency details
        services_offered: stringToArray(data.details.services_offered_text),
        specializations: stringToArray(data.details.specializations_text),
        // Convert text fields to arrays for newsletter details
        topics_covered: stringToArray(data.details.topics_covered_text),
        // Remove the text versions
        key_features_text: undefined,
        use_cases_text: undefined,
        target_audience_text: undefined,
        support_channels_text: undefined,
        prerequisites_text: undefined,
        learning_outcomes_text: undefined,
        services_offered_text: undefined,
        specializations_text: undefined,
        topics_covered_text: undefined,
      } : undefined;

      const payload: CreateEntityDto = {
        ...data,
        category_ids: selectedCategoryIds.length > 0 ? selectedCategoryIds : undefined,
        tag_ids: selectedTagIds.length > 0 ? selectedTagIds : undefined,
        feature_ids: selectedFeatureIds.length > 0 ? selectedFeatureIds : undefined,
        // Convert empty strings to undefined for optional URL fields
        logo_url: data.logo_url || undefined,
        documentation_url: data.documentation_url || undefined,
        contact_url: data.contact_url || undefined,
        privacy_policy_url: data.privacy_policy_url || undefined,
        ref_link: data.ref_link || undefined,
        meta_title: data.meta_title || undefined,
        meta_description: data.meta_description || undefined,
        details: processedDetails,
      };

      await onSubmit(payload);
    } catch (error) {
      console.error('Form submission error:', error);
    }
  };

  const selectedEntityType = entityTypes.find(type => type.id === selectedEntityTypeId);

  const renderTypeSpecificDetails = () => {
    if (!selectedEntityType) return null;

    const commonProps = {
      register,
      errors,
      setValue,
      watch
    };

    switch (selectedEntityType.slug) {
      case 'ai-tool':
      case 'tool':
        return <ToolDetailsForm {...commonProps} />;
      case 'course':
        return <CourseDetailsForm {...commonProps} />;
      case 'agency':
        return <AgencyDetailsForm {...commonProps} />;
      case 'newsletter':
        return <NewsletterDetailsForm {...commonProps} />;
      case 'community':
      case 'content-creator':
      default:
        return <DefaultDetailsForm {...commonProps} entityTypeName={selectedEntityType.name} />;
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-8">
      {/* Step 1: Entity Type Selection */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="entityType" className="text-base font-semibold">
            What kind of resource are you submitting? *
          </Label>
          <Select onValueChange={handleEntityTypeChange} value={selectedEntityTypeId}>
            <SelectTrigger className="mt-2">
              <SelectValue placeholder="Select a resource type..." />
            </SelectTrigger>
            <SelectContent>
              {entityTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.entity_type_id && (
            <p className="mt-1 text-sm text-red-600">{errors.entity_type_id.message}</p>
          )}
        </div>

        {selectedEntityType && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900">{selectedEntityType.name}</h3>
            <p className="text-sm text-blue-700 mt-1">{selectedEntityType.description}</p>
          </div>
        )}
      </div>

      {/* Step 2: Core Information (shown after entity type is selected) */}
      {selectedEntityTypeId && (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Core Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <Label htmlFor="name">Resource Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter the name of the resource"
                  className="mt-1"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="website_url">Website URL *</Label>
                <Input
                  id="website_url"
                  type="url"
                  {...register('website_url')}
                  placeholder="https://example.com"
                  className="mt-1"
                />
                {errors.website_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.website_url.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="short_description">Short Description</Label>
                <Input
                  id="short_description"
                  {...register('short_description')}
                  placeholder="Brief one-line description (optional)"
                  className="mt-1"
                />
                {errors.short_description && (
                  <p className="mt-1 text-sm text-red-600">{errors.short_description.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="description">Full Description *</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Provide a detailed description of the resource, its features, and benefits..."
                  className="mt-1 min-h-[120px]"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="logo_url">Logo URL</Label>
                <Input
                  id="logo_url"
                  type="url"
                  {...register('logo_url')}
                  placeholder="https://example.com/logo.png (optional)"
                  className="mt-1"
                />
                {errors.logo_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.logo_url.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="documentation_url">Documentation URL</Label>
                <Input
                  id="documentation_url"
                  type="url"
                  {...register('documentation_url')}
                  placeholder="https://docs.example.com (optional)"
                  className="mt-1"
                />
                {errors.documentation_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.documentation_url.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Categorization Section */}
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Categorization</h2>

            <div className="space-y-6">
              <MultiSelectCheckbox
                label="Categories"
                options={categories.map(cat => ({
                  id: cat.id,
                  name: cat.name,
                  description: cat.description
                }))}
                selectedIds={selectedCategoryIds}
                onSelectionChange={setSelectedCategoryIds}
                showDescription={true}
                columns={2}
              />

              <MultiSelectCheckbox
                label="Tags"
                options={tags.map(tag => ({
                  id: tag.id,
                  name: tag.name
                }))}
                selectedIds={selectedTagIds}
                onSelectionChange={setSelectedTagIds}
                columns={3}
              />

              <MultiSelectCheckbox
                label="Features"
                options={features.map(feature => ({
                  id: feature.id,
                  name: feature.name,
                  description: feature.description
                }))}
                selectedIds={selectedFeatureIds}
                onSelectionChange={setSelectedFeatureIds}
                showDescription={true}
                columns={2}
              />
            </div>
          </div>

          {/* Additional Fields Section */}
          <div className="border-t pt-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="contact_url">Contact URL</Label>
                <Input
                  id="contact_url"
                  type="url"
                  {...register('contact_url')}
                  placeholder="https://example.com/contact (optional)"
                  className="mt-1"
                />
                {errors.contact_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.contact_url.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="privacy_policy_url">Privacy Policy URL</Label>
                <Input
                  id="privacy_policy_url"
                  type="url"
                  {...register('privacy_policy_url')}
                  placeholder="https://example.com/privacy (optional)"
                  className="mt-1"
                />
                {errors.privacy_policy_url && (
                  <p className="mt-1 text-sm text-red-600">{errors.privacy_policy_url.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="founded_year">Founded Year</Label>
                <Input
                  id="founded_year"
                  type="number"
                  {...register('founded_year', { valueAsNumber: true })}
                  placeholder="2023 (optional)"
                  className="mt-1"
                  min="1900"
                  max={new Date().getFullYear()}
                />
                {errors.founded_year && (
                  <p className="mt-1 text-sm text-red-600">{errors.founded_year.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="ref_link">Referral Link</Label>
                <Input
                  id="ref_link"
                  type="url"
                  {...register('ref_link')}
                  placeholder="https://example.com/ref/123 (optional)"
                  className="mt-1"
                />
                {errors.ref_link && (
                  <p className="mt-1 text-sm text-red-600">{errors.ref_link.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Type-Specific Details Section */}
          {selectedEntityType && (
            <div className="border-t pt-6">
              {renderTypeSpecificDetails()}
            </div>
          )}

          {/* Submit button */}
          <div className="border-t pt-6">
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full md:w-auto"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Resource'}
            </Button>
          </div>
        </div>
      )}
    </form>
  );
}

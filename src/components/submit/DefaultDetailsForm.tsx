'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface DefaultDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: (name: string, value: any) => void;
  watch: (name: string) => any;
  entityTypeName: string;
}

export default function DefaultDetailsForm({ 
  register, 
  errors, 
  setValue, 
  watch, 
  entityTypeName 
}: DefaultDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">{entityTypeName} Details</h3>
      
      <div>
        <Label htmlFor="additional_details">Additional Details</Label>
        <Textarea
          id="additional_details"
          {...register('details.additional_details')}
          placeholder={`Enter any specific details about this ${entityTypeName.toLowerCase()}...`}
          className="mt-1 min-h-[120px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Provide any additional information that would be helpful for users to know about this resource.
        </p>
      </div>
    </div>
  );
}

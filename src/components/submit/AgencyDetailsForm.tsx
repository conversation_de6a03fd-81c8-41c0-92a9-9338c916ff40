'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface AgencyDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: (name: string, value: any) => void;
  watch: (name: string) => any;
}

export default function AgencyDetailsForm({ register, errors, setValue, watch }: AgencyDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Agency Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Portfolio URL */}
        <div>
          <Label htmlFor="portfolioUrl">Portfolio URL</Label>
          <Input
            id="portfolioUrl"
            type="url"
            {...register('details.portfolioUrl')}
            placeholder="https://agency.com/portfolio"
            className="mt-1"
          />
        </div>

        {/* Team Size */}
        <div>
          <Label htmlFor="teamSize">Team Size</Label>
          <Input
            id="teamSize"
            {...register('details.teamSize')}
            placeholder="10-50 employees"
            className="mt-1"
          />
        </div>

        {/* Contact Email */}
        <div>
          <Label htmlFor="contactEmail">Contact Email</Label>
          <Input
            id="contactEmail"
            type="email"
            {...register('details.contactEmail')}
            placeholder="<EMAIL>"
            className="mt-1"
          />
        </div>

        {/* Region Served */}
        <div>
          <Label htmlFor="regionServed">Region Served</Label>
          <Input
            id="regionServed"
            {...register('details.regionServed')}
            placeholder="North America, Europe, Global..."
            className="mt-1"
          />
        </div>
      </div>

      {/* Services Offered */}
      <div>
        <Label htmlFor="servicesOffered">Services Offered (comma-separated)</Label>
        <Textarea
          id="servicesOffered"
          {...register('details.servicesOfferedText')}
          placeholder="Web development, Mobile apps, AI consulting, Digital marketing..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter services separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Specializations */}
      <div>
        <Label htmlFor="specializations">Specializations (comma-separated)</Label>
        <Textarea
          id="specializations"
          {...register('details.specializationsText')}
          placeholder="E-commerce, SaaS, Healthcare, Fintech..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter specializations separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}

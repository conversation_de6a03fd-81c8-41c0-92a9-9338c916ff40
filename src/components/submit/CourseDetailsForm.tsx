'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface CourseDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: (name: string, value: any) => void;
  watch: (name: string) => any;
}

export default function CourseDetailsForm({ register, errors, setValue, watch }: CourseDetailsFormProps) {
  const certificateAvailable = watch('details.certificateAvailable');

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Course Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Instructor Name */}
        <div>
          <Label htmlFor="instructorName">Instructor Name</Label>
          <Input
            id="instructorName"
            {...register('details.instructorName')}
            placeholder="John Doe"
            className="mt-1"
          />
        </div>

        {/* Duration */}
        <div>
          <Label htmlFor="durationText">Duration</Label>
          <Input
            id="durationText"
            {...register('details.durationText')}
            placeholder="8 weeks, 40 hours, etc."
            className="mt-1"
          />
        </div>

        {/* Skill Level */}
        <div>
          <Label htmlFor="level">Skill Level</Label>
          <Select onValueChange={(value) => setValue('details.level', value)}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select skill level..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Beginner">Beginner</SelectItem>
              <SelectItem value="Intermediate">Intermediate</SelectItem>
              <SelectItem value="Advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Language */}
        <div>
          <Label htmlFor="language">Language</Label>
          <Input
            id="language"
            {...register('details.language')}
            placeholder="English, Spanish, etc."
            className="mt-1"
          />
        </div>
      </div>

      {/* Certificate Available */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="certificateAvailable"
          checked={certificateAvailable}
          onCheckedChange={(checked) => setValue('details.certificateAvailable', checked)}
        />
        <Label htmlFor="certificateAvailable">Certificate Available</Label>
      </div>

      {/* Prerequisites */}
      <div>
        <Label htmlFor="prerequisites">Prerequisites (comma-separated)</Label>
        <Textarea
          id="prerequisites"
          {...register('details.prerequisitesText')}
          placeholder="Basic programming knowledge, HTML/CSS, JavaScript fundamentals..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter prerequisites separated by commas. They will be converted to a list.
        </p>
      </div>

      {/* Learning Outcomes */}
      <div>
        <Label htmlFor="learningOutcomes">Learning Outcomes (comma-separated)</Label>
        <Textarea
          id="learningOutcomes"
          {...register('details.learningOutcomesText')}
          placeholder="Build web applications, Understand React concepts, Deploy to production..."
          className="mt-1 min-h-[100px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter learning outcomes separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}

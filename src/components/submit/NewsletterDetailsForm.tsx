'use client';

import React from 'react';
import { UseFormRegister, FieldErrors } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface NewsletterDetailsFormProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  setValue: (name: string, value: any) => void;
  watch: (name: string) => any;
}

export default function NewsletterDetailsForm({ register, errors, setValue, watch }: NewsletterDetailsFormProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">Newsletter Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Author Name */}
        <div>
          <Label htmlFor="authorName">Author Name</Label>
          <Input
            id="authorName"
            {...register('details.authorName')}
            placeholder="<PERSON>"
            className="mt-1"
          />
        </div>

        {/* Publication Schedule */}
        <div>
          <Label htmlFor="publicationSchedule">Publication Schedule</Label>
          <Input
            id="publicationSchedule"
            {...register('details.publicationSchedule')}
            placeholder="Weekly, Bi-weekly, Monthly..."
            className="mt-1"
          />
        </div>

        {/* Archive URL */}
        <div>
          <Label htmlFor="archiveUrl">Archive URL</Label>
          <Input
            id="archiveUrl"
            type="url"
            {...register('details.archiveUrl')}
            placeholder="https://newsletter.com/archive"
            className="mt-1"
          />
        </div>

        {/* Subscription Link */}
        <div>
          <Label htmlFor="subscriptionLink">Subscription Link</Label>
          <Input
            id="subscriptionLink"
            type="url"
            {...register('details.subscriptionLink')}
            placeholder="https://newsletter.com/subscribe"
            className="mt-1"
          />
        </div>
      </div>

      {/* Target Audience */}
      <div>
        <Label htmlFor="targetAudience">Target Audience</Label>
        <Input
          id="targetAudience"
          {...register('details.targetAudience')}
          placeholder="Developers, Entrepreneurs, AI enthusiasts..."
          className="mt-1"
        />
      </div>

      {/* Topics Covered */}
      <div>
        <Label htmlFor="topicsCovered">Topics Covered (comma-separated)</Label>
        <Textarea
          id="topicsCovered"
          {...register('details.topicsCoveredText')}
          placeholder="AI news, Tech trends, Industry insights, Product reviews..."
          className="mt-1 min-h-[80px]"
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter topics separated by commas. They will be converted to a list.
        </p>
      </div>
    </div>
  );
}

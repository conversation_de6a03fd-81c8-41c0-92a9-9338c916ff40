export interface EntityType {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  parentCategoryId?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Tag {
  id: string;
  name: string;
  slug: string;
}

export interface Feature {
  id: string;
  name: string;
  slug: string;
  description: string;
  iconUrl?: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface SocialLinks {
  twitter?: string;
  linkedin?: string;
  github?: string;
  youtube?: string;
  facebook?: string;
  instagram?: string;
  discord?: string;
  [key: string]: string | undefined;
}

export interface Submitter {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at?: string | null;
  user_metadata: {
    username?: string | null;
    display_name?: string | null;
    profile_picture_url?: string | null;
    internal_user_id?: string;
  };
}

// Updated details interface based on actual API response
export interface EntityDetails {
  entityId: string;
  programmingLanguages?: string[] | null;
  frameworks?: string[] | null;
  libraries?: string[] | null;
  integrations?: string[] | null;
  keyFeatures?: string[];
  useCases?: string[];
  targetAudience?: string[];
  learningCurve?: 'LOW' | 'MEDIUM' | 'HIGH' | null;
  deploymentOptions?: string[] | null;
  supportedOs?: string[] | null;
  mobileSupport?: boolean;
  apiAccess?: boolean;
  customizationLevel?: string | null;
  trialAvailable?: boolean;
  demoAvailable?: boolean;
  openSource?: boolean;
  supportChannels?: string[];
  hasFreeTier?: boolean;
  pricingModel?: 'FREE' | 'FREEMIUM' | 'PAID' | 'SUBSCRIPTION' | 'ONE_TIME' | null;
  priceRange?: 'FREE' | 'LOW' | 'MEDIUM' | 'HIGH' | 'ENTERPRISE' | null;
  pricingDetails?: string | null;
  pricingUrl?: string | null;
  supportEmail?: string | null;
  hasLiveChat?: boolean | null;
  communityUrl?: string | null;
  [key: string]: unknown; // Allow other dynamic fields
}

// Legacy detail interfaces for backward compatibility
export interface ToolDetails {
  technical_level?: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' | string;
  key_features?: string[];
  integrations?: string[];
  use_cases?: string[];
  pricing_model?: 'Free' | 'Freemium' | 'Paid' | 'Subscription' | 'One-time Purchase' | string;
  api_available?: boolean;
  self_hosted_option?: boolean;
  [key: string]: unknown;
}

export interface CourseDetails {
  instructor_name?: string;
  duration_text?: string;
  level?: 'Beginner' | 'Intermediate' | 'Advanced' | string;
  certificate_available?: boolean;
  prerequisites?: string[];
  learning_outcomes?: string[];
  language?: string;
  [key: string]: unknown;
}

export interface AgencyDetails {
  services_offered?: string[];
  portfolio_url?: string;
  team_size?: number | string;
  specializations?: string[];
  contact_email?: string;
  region_served?: string;
  [key: string]: unknown;
}

export interface ContentCreatorDetails {
  platform?: 'YouTube' | 'TikTok' | 'Twitch' | 'Instagram' | 'Blog' | 'Podcast' | string;
  platform_url?: string;
  subscriber_count?: string;
  content_focus?: string[];
  collaboration_email?: string;
  sample_work_links?: string[];
  [key: string]: unknown;
}

export interface CommunityDetails {
  platform_name?: string;
  platform_url?: string;
  member_count?: string;
  main_topics?: string[];
  moderator_info?: string;
  entry_requirements?: string;
  [key: string]: unknown;
}

export interface NewsletterDetails {
  publication_schedule?: string;
  target_audience?: string;
  archive_url?: string;
  subscription_link?: string;
  author_name?: string;
  topics_covered?: string[];
  [key: string]: unknown;
}

export interface Entity {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  websiteUrl: string;
  logoUrl: string | null;
  documentationUrl?: string | null;
  contactUrl?: string | null;
  privacyPolicyUrl?: string | null;
  foundedYear?: number | null;
  entityType: EntityType;
  categories: Category[];
  tags: Tag[];
  features: Feature[];
  avgRating: number;
  reviewCount: number;
  saveCount: number;
  status: string; // e.g., 'ACTIVE', 'PENDING'
  socialLinks?: SocialLinks | null;
  submitter?: Submitter;
  legacyId?: string | null;
  createdAt: string;
  updatedAt: string;
  metaTitle?: string;
  metaDescription?: string;
  scrapedReviewSentimentLabel?: string | null;
  scrapedReviewSentimentScore?: number | null;
  scrapedReviewCount?: number | null;
  employeeCountRange?: string | null;
  fundingStage?: string | null;
  locationSummary?: string | null;
  refLink?: string;
  affiliateStatus?: string;
  hasFreeTier?: boolean;
  details?: EntityDetails;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PaginatedEntities {
  data: Entity[];
  meta: PaginationMeta;
}

// Interface for query parameters for fetching entities
export interface GetEntitiesParams {
  page?: number;
  limit?: number;
  searchTerm?: string;
  categoryIds?: string[];
  tagIds?: string[];
  entityTypeIds?: string[];
  entityTypeId?: string; // Single entity type filter
  featureIds?: string[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: string;
  submitterId?: string;

  // Date filters
  createdAtFrom?: string;
  createdAtTo?: string;

  // Boolean filters
  hasFreeTier?: boolean;
  apiAccess?: boolean;

  // Array filters
  employeeCountRanges?: string[];
  fundingStages?: string[];
  pricingModels?: string[];
  priceRanges?: string[];
  integrations?: string[];
  platforms?: string[];
  targetAudience?: string[];

  // Location search
  locationSearch?: string;
}

// Interface for creating a new entity (mirrors backend CreateEntityDto)
export interface CreateEntityDto {
  // Core entity information
  name: string;
  shortDescription?: string;
  description: string;
  websiteUrl: string;
  logoUrl?: string;
  documentationUrl?: string;
  contactUrl?: string;
  privacyPolicyUrl?: string;
  foundedYear?: number;

  // Entity type and categorization
  entityTypeId: string;
  categoryIds?: string[];
  tagIds?: string[];
  featureIds?: string[];

  // Social links
  socialLinks?: SocialLinks;

  // SEO metadata
  metaTitle?: string;
  metaDescription?: string;

  // Company/organization details
  employeeCountRange?: string;
  fundingStage?: string;
  locationSummary?: string;

  // Pricing and access
  hasFreeTier?: boolean;
  refLink?: string;
  affiliateStatus?: string;

  // Type-specific details (dynamic based on entity type)
  details?: EntityDetails | ToolDetails | CourseDetails | AgencyDetails | ContentCreatorDetails | CommunityDetails | NewsletterDetails;
}